#!/usr/bin/env python3
"""
Test script for the clean multi-MCP client to verify compatibility.
"""

import asyncio
import logging
from multi_mcp_client_clean import MultiMCPClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_clean_client():
    """Test the clean client to verify it has the expected interface."""

    client = MultiMCPClient()

    try:
        # Test that the connections attribute exists
        print(f"Has connections attribute: {hasattr(client, 'connections')}")
        print(f"Connections type: {type(client.connections)}")
        print(f"Connections content: {client.connections}")

        # Test that debug_callback exists
        print(f"Has debug_callback: {hasattr(client, 'debug_callback')}")

        # Test that spawn_server exists
        print(f"Has spawn_server: {hasattr(client, 'spawn_server')}")

        # Test that add_server exists
        print(f"Has add_server: {hasattr(client, 'add_server')}")

        # Test that call_tool exists
        print(f"Has call_tool: {hasattr(client, 'call_tool')}")

        # Test that cleanup exists
        print(f"Has cleanup: {hasattr(client, 'cleanup')}")

        # Test connections behavior like the original
        print(f"Connections items() works: {hasattr(client.connections, 'items')}")
        print(f"Can iterate connections: {list(client.connections.items())}")

        # Test that connections can be checked for truthiness
        active_connections = [k for k, v in client.connections.items() if v]
        print(f"Active connections filter works: {active_connections}")

        print("All required methods and properties exist!")

    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

    finally:
        await client.cleanup()


if __name__ == "__main__":
    asyncio.run(test_clean_client())
