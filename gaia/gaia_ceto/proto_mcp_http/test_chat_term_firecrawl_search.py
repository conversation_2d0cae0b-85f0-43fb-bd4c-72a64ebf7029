#!/usr/bin/env python3
"""
Test script for chat_term with Firecrawl search functionality.

This test:
1. Starts the complete MCP server
2. Creates a new conversation in chat_term
3. Issues a prompt to search for recent AI news
4. Asserts that the firecrawl search tool was used
5. Asserts that the search executed successfully
6. Validates that results were returned
7. Cleans up the server and chat_term

Requirements:
- FIRECRAWL_API_KEY environment variable must be set
- The complete server configuration must have firecrawl-hosted enabled
"""

import asyncio
import json
import logging
import os
import signal
import subprocess
import sys
import tempfile
import time
import unittest
from typing import List, Dict, Any, Optional
from unittest.mock import patch

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ToolCallTracker:
    """Tracks tool calls made during the test."""
    
    def __init__(self):
        self.tool_calls: List[Dict[str, Any]] = []
        self.firecrawl_search_called = False
        self.firecrawl_search_successful = False
        self.search_results = None
    
    def debug_callback(self, level: str, message: str, data: Any = None):
        """Debug callback to track tool calls."""
        # Log the debug message
        logger.debug(f"[{level.upper()}] {message}")
        
        # Track tool calls
        if "Calling tool:" in message and "web__firecrawl_search" in message:
            self.firecrawl_search_called = True
            logger.info("🔍 Detected Firecrawl search tool call!")
        
        if "Tool 'web__firecrawl_search' executed successfully" in message:
            self.firecrawl_search_successful = True
            self.search_results = data
            logger.info("✅ Firecrawl search executed successfully!")
        
        # Store all tool call information
        if "Calling tool:" in message:
            self.tool_calls.append({
                "level": level,
                "message": message,
                "data": data,
                "timestamp": time.time()
            })


class TestChatTermFirecrawlSearch(unittest.TestCase):
    """Test chat_term with Firecrawl search functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.server_process = None
        self.temp_dir = tempfile.mkdtemp()
        self.tracker = ToolCallTracker()
        
        # Check for required environment variable
        if not os.getenv("FIRECRAWL_API_KEY"):
            self.skipTest("FIRECRAWL_API_KEY environment variable not set")
    
    def tearDown(self):
        """Clean up test environment."""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.server_process.kill()
                self.server_process.wait()
        
        # Clean up temp directory
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def start_complete_server(self, port: int = 9000) -> subprocess.Popen:
        """Start the complete MCP server."""
        logger.info(f"Starting complete MCP server on port {port}...")
        
        # Get the server script path
        server_script = os.path.join(
            os.path.dirname(__file__),
            "mcp_http_server_multi.py"
        )
        
        # Start the server process
        env = os.environ.copy()
        env["PYTHONPATH"] = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
        
        process = subprocess.Popen(
            [sys.executable, server_script, "--port", str(port), "--config", "server_config.json"],
            cwd=os.path.dirname(__file__),
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for server to start
        max_wait = 30
        for i in range(max_wait):
            if process.poll() is not None:
                stdout, stderr = process.communicate()
                raise RuntimeError(f"Server failed to start:\nSTDOUT: {stdout}\nSTDERR: {stderr}")
            
            try:
                # Test if server is responding by attempting MCP connection
                async def check_server():
                    try:
                        test_client = MCPClientLib()
                        success = await test_client.connect_to_server(f"http://localhost:{port}/mcp")
                        if success:
                            await test_client.cleanup()
                        return success
                    except Exception as e:
                        logger.debug(f"Server check failed: {e}")
                        return False

                # Use asyncio.new_event_loop to avoid conflicts
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    if loop.run_until_complete(check_server()):
                        logger.info("✅ Server is responding")
                        break
                finally:
                    loop.close()
            except Exception as e:
                logger.debug(f"Server check exception: {e}")
                pass
            
            time.sleep(1)
        else:
            process.terminate()
            raise RuntimeError(f"Server did not start within {max_wait} seconds")
        
        return process
    
    def test_firecrawl_search_integration(self):
        """Test the complete Firecrawl search integration."""
        async def run_test():
            # Start the server
            self.server_process = self.start_complete_server()

            try:
                # Wait for server to fully initialize
                await asyncio.sleep(3)

                # Test direct MCP client connection first
                client = MCPClientLib(debug_callback=self.tracker.debug_callback)
                success = await client.connect_to_server("http://localhost:9000/mcp")
                self.assertTrue(success, "Failed to connect to MCP server")

                # Check that firecrawl search tool is available
                tool_names = [tool['name'] for tool in client.available_tools]
                logger.info(f"Available tools: {tool_names}")

                firecrawl_search_available = any("firecrawl_search" in name for name in tool_names)
                self.assertTrue(firecrawl_search_available, "Firecrawl search tool not available")

                # Find the exact tool name
                firecrawl_tool_name = None
                for name in tool_names:
                    if "firecrawl_search" in name:
                        firecrawl_tool_name = name
                        break

                logger.info(f"Using Firecrawl tool: {firecrawl_tool_name}")

                # Test direct tool call first
                logger.info("Testing direct Firecrawl search tool call...")
                search_result = await client.call_tool(
                    firecrawl_tool_name,
                    {"query": "artificial intelligence news", "limit": 3},
                    timeout=30
                )

                self.assertTrue(search_result.success, f"Direct tool call failed: {search_result.error}")
                self.assertIsNotNone(search_result.content, "No content returned from direct tool call")
                logger.info("✅ Direct tool call successful")

                # Now test via process_query (simulating chat_term behavior)
                logger.info("Testing via process_query (chat_term simulation)...")
                query_result = await client.process_query(
                    "Search for recent news about artificial intelligence developments",
                    tool_timeout=30
                )

                self.assertIsNone(query_result["error"], f"Query processing failed: {query_result['error']}")
                self.assertGreater(len(query_result["tool_results"]), 0, "No tools were called")

                # Check if firecrawl search was used
                firecrawl_used = any(
                    firecrawl_tool_name in str(result.tool_name)
                    for result in query_result["tool_results"]
                )
                self.assertTrue(firecrawl_used, "Firecrawl search was not used in query processing")

                # Verify successful execution
                firecrawl_results = [
                    result for result in query_result["tool_results"]
                    if firecrawl_tool_name in str(result.tool_name)
                ]
                self.assertGreater(len(firecrawl_results), 0, "No Firecrawl results found")

                for result in firecrawl_results:
                    self.assertTrue(result.success, f"Firecrawl tool failed: {result.error}")
                    self.assertIsNotNone(result.content, "Firecrawl tool returned no content")

                # Verify final response contains content
                self.assertGreater(
                    len(query_result["final_text"]),
                    50,
                    "Final response should contain substantial content"
                )

                logger.info("✅ All assertions passed!")
                logger.info(f"Tool calls made: {len(query_result['tool_results'])}")
                logger.info(f"Final response length: {len(query_result['final_text'])}")

                # Update tracker state for compatibility
                self.tracker.firecrawl_search_called = True
                self.tracker.firecrawl_search_successful = True
                self.tracker.search_results = firecrawl_results

                await client.cleanup()

            except Exception as e:
                logger.error(f"Test failed with exception: {e}")
                raise

        # Run the async test
        asyncio.run(run_test())
    
    def test_firecrawl_search_integration_sync(self):
        """Synchronous wrapper for the async test."""
        asyncio.run(self.test_firecrawl_search_integration())

    def test_server_startup_and_tools(self):
        """Test that the server starts and has the expected tools."""
        # Start the server
        self.server_process = self.start_complete_server()

        async def check_tools():
            client = MCPClientLib()
            success = await client.connect_to_server("http://localhost:9000/mcp")
            self.assertTrue(success, "Failed to connect to server")

            tool_names = [tool['name'] for tool in client.available_tools]
            logger.info(f"Available tools: {tool_names}")

            # Check for expected tools (updated to match actual server tools)
            expected_tools = ["echostring", "long_task", "echo"]
            for tool in expected_tools:
                self.assertIn(tool, tool_names, f"Expected tool '{tool}' not found")

            # Check for firecrawl tools
            firecrawl_tools = [name for name in tool_names if "firecrawl" in name]
            self.assertGreater(len(firecrawl_tools), 0, "No Firecrawl tools found")
            logger.info(f"Firecrawl tools: {firecrawl_tools}")

            await client.cleanup()

        asyncio.run(check_tools())


def main():
    """Run the test."""
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    print("🧪 Starting Firecrawl Search Integration Test")
    print("=" * 50)
    print("This test will:")
    print("1. Start the complete MCP server")
    print("2. Test Firecrawl search tool availability")
    print("3. Execute a search query")
    print("4. Verify successful execution and results")
    print("5. Clean up resources")
    print("=" * 50)

    # Run the test
    unittest.main(verbosity=2, exit=False)


if __name__ == "__main__":
    main()
